<template>
  <div>
    <el-dialog
      title="答题卡设置"
      :visible.sync="dialogVisible"
      width="800px"
      :before-close="handleClose"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      v-loading.fullscreen.lock="isLoading"
      element-loading-text="客户端启动中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
    >
      <div class="dialog-body">
        <el-form ref="form" :model="thirdCardInfo" label-width="140px">
          <el-form-item label="尺寸">
            <el-radio-group v-model="thirdCardInfo.size">
              <el-radio label="A3">A3</el-radio>
              <el-radio label="A4">A4</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="页面">
            <el-radio-group v-model="thirdCardInfo.pageType" @change="sendChangePage">
              <el-radio label="1">1面</el-radio>
              <el-radio label="2">2面</el-radio>
              <el-radio label="3">3面</el-radio>
              <el-radio label="4">4面</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="版式" v-if="thirdCardInfo.size == 'A3'">
            <el-radio-group v-model="thirdCardInfo.pageLayout">
              <el-radio :label="IPAGELAYOUT.A3">两栏</el-radio>
              <el-radio :label="IPAGELAYOUT.A33">三栏</el-radio>
              <el-radio :label="IPAGELAYOUT.A32">正三反二</el-radio>
              <el-radio :label="IPAGELAYOUT.A23">正二反三</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="上传或扫描答题卡">
            <span class="tip"> 提示：请上传或扫描未作答的答题卡图片 </span>
            <div class="upload-container">
              <div :class="['img-list',{'more': filePathList.length > 2}]" v-for="(item, index) in filePathList" :key="index">
                <div
                  class="img-list-item"
                  :style="{ transform: `rotate(${getImageRotation(item.id)}deg)` }"
                  @click="openPreview(index)"
                >
                  <img :src="getOriginalImageUrl(item.url)" alt="答题卡" />
                </div>
                <span class="img-page">第{{ Math.floor(index/2) + 1 }}张{{ index%2 == 0 ? '正' : '反' }}面</span>
                <i
                  class="exchange-icon el-icon-sort"
                  v-if="filePathList.length == 2 && index == 0"
                  @click="handleExchange(item)"
                ></i>
                <i class="rotate-icon el-icon-refresh-right" @click="handleRotate(item)"></i>
                <i class="delete-icon el-icon-delete" @click="handleRemove(item)"></i>
              </div>
              <div v-if="!filePathList.length || showUploadType == 'scan'" @click="startApp" class="upload scan-upload">
                <div class="upload-icon">
                  <div class="icon-scan">
                  </div>
                  <p >扫描答题卡</p>
                </div>
              </div>
              <el-upload
                v-if="!filePathList.length || showUploadType == 'upload'"
                :disabled="disabledUpload"
                ref="uploadFile"
                :action="uploadUrl"
                :accept="coverAccept"
                :show-file-list="false"
                :on-success="uploadSuccess"
                :before-upload="beforeUpload"
                :data="uploadData"
                :http-request="ossUpload"
                type="drag"
                class="upload"
                :class="{ disabledUpload: disabledUpload }"
              >
                <div class="upload-icon">
                  <div class="icon-photo">
                  </div>
                  <p style="line-height: 18px;">上传文件</p>
                  <p style="font-size: 10px;line-height: 10px;">(jpg,png,pdf)</p>
                </div>
              </el-upload>
            </div>
          </el-form-item>
          <el-form-item v-if="filePathList.length > 0">
            <p>注：</p>
            <p>1、请确保答题卡图片方向正确，如需调整，请旋转图片;</p>
            <p>2、请确保答题卡图片依次排列，如需调整，请点击互换按钮对调顺序;</p>
            <p>3、点击图片可查看大图</p>
          </el-form-item>
          <el-form-item v-if="tipType">
            <p v-if="tipType == 1" style="color:red;">检测到尚未安装C30扫描，请<a target="_blank" href="https://fs.iclass30.com/scan/personal-scan.exe">下载</a>安装后再进行扫描</p>
            <p v-else-if="tipType == 2" style="color:red;">扫描仪连接异常</p>
            <p v-else-if="tipType == 3" style="color:red;">检测到纸张</p>
            <p v-else-if="tipType == 4">扫描中...</p>
            <p v-else-if="tipType == 5" style="color:#0f6400">C30扫描客户端已运行</p>
            <p v-else-if="tipType == 6" style="color:#0f6400">扫描仪已连接，点击<el-button type="text" @click="startScan">开始扫描</el-button></p>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="confirmCreate">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 自定义图片预览组件 -->
    <div v-if="previewVisible" class="custom-image-preview" @click="closePreview">
      <div class="preview-container" @click.stop>
        <div class="preview-header">
          <span class="preview-title">图片预览 ({{ currentPreviewIndex + 1 }}/{{ filePathList.length }})</span>
          <i class="el-icon-close preview-close" @click="closePreview"></i>
        </div>
        <div class="preview-body">
          <div class="preview-controls">
            <i class="el-icon-arrow-left" @click="prevImage" v-if="filePathList.length > 1"></i>
            <div class="preview-image-container">
              <img
                ref="previewImage"
                :src="getOriginalImageUrl(filePathList[currentPreviewIndex].url)"
                :style="{
                  transform: `rotate(${getImageRotation(filePathList[currentPreviewIndex].id)}deg) scale(${previewScale})`,
                  maxWidth: getImageMaxSize().width,
                  maxHeight: getImageMaxSize().height
                }"
                @wheel="handleWheel"
                class="preview-image"
              />
            </div>
            <i class="el-icon-arrow-right" @click="nextImage" v-if="filePathList.length > 1"></i>
          </div>
          <div class="preview-toolbar">
            <i class="el-icon-zoom-out" @click="zoomOut" title="缩小"></i>
            <span class="zoom-text">{{ Math.round(previewScale * 100) }}%</span>
            <i class="el-icon-zoom-in" @click="zoomIn" title="放大"></i>
            <i class="el-icon-refresh-left" @click="rotatePreviewImageLeft" title="向左旋转"></i>
            <i class="el-icon-refresh-right" @click="rotatePreviewImageRight" title="向右旋转"></i>
            <i class="el-icon-full-screen" @click="resetView" title="适应窗口"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import ossUploadFile from '@/utils/ossUploadFile';
import { guid,get_suffix,generateUUID } from '@/utils/index';
import socket from '@/utils/socket';
import { IPAGELAYOUT } from '@/typings/card';
import { getPdfImage } from '@/service/xueban';

export default {
  props: {
    dialogVisible: {
      type: Boolean,
      required: true,
    },
  },
  computed: {
    ...mapState(['loginInfo', 'schoolInfo']),
    disabledUpload() {
      return (
        (this.thirdCardInfo.pageType == 1 && this.filePathList.length >= 1) ||
        (this.thirdCardInfo.pageType == 2 && this.filePathList.length >= 2) ||
        (this.thirdCardInfo.pageType == 3 && this.filePathList.length >= 3) ||
        (this.thirdCardInfo.pageType == 4 && this.filePathList.length >= 4)
      );
    },
  },
  data() {
    return {
      IPAGELAYOUT,
      thirdCardInfo: {
        size: 'A3',
        pageType: '2', //0:双面 1:单面
        pageLayout: IPAGELAYOUT.A3 , //：1 A4单栏  2：A3两栏  3：A3三栏 4:正3反2 5:正2反3
      },
      coverAccept: '.jpg,.png,.pdf',
      uploadUrl: '', //word上传地址
      uploadData: {}, //word上传所附带的参数
      filePath:'gece_image/thirdCard',
      filePathList: [], //选择的文件上传地址
      srcList: [],
      fsUrl: process.env.VUE_APP_FS_URL, //地址
      roomId: generateUUID().replace(/-/g,''),
      showUploadType:"",
      TIPTYPE:{
        UN_START:0,
        UN_INSTALL:1,
        SCAN_CONNECT_FAIL:2,
        NO_PAPER:3,
        SCANING:4,
        APP_START:5,
        SCAN_CONNECT_SUCCESS:6
      },
      tipType:"", //0:未启动 1:未安装 2:扫描仪连接异常 3:未检测到纸张 4:扫描中 5:C30扫描客户端已运行 6:扫描仪连接成功
      decviceCode:"",
      isLoading:false,
      // 自定义预览相关数据
      previewVisible: false,
      currentPreviewIndex: 0,
      imageRotations: {}, // 存储每张图片的旋转角度
      previewScale: 1, // 预览缩放比例
    };
  },
  created() {
      // 加入房间
      socket.joinRoom(this.roomId);
      // 监听客户端上线
      socket.addListener('scanClientOnline', this.scanClientOnline);
      // 监听客户端下线
      socket.addListener('scanClientOffline', this.scanClientOffline);
      // 监听扫描仪链接成功
      socket.addListener('deviceConnectioned', this.deviceConnectioned);
      // 监听扫描上传图片
      socket.addListener('scanThirdCardSuccess', this.scanThirdCardSuccess);
      // 监听客户端握手
      // socket.addListener('clientReady', this.clientReady);
      // socket.addListener('clientClose', this.clientClose);
      // this.decviceCode = this.$localSave.get('decviceCode');
      // if (this.decviceCode) {
      //   socket.joinRoom(this.decviceCode);
      //   this.sendShakeHands(this.decviceCode);
      // }
  },
  destroyed(){
    if (this.onLineData) {
      let data = {
        type: 'closeExamHandle',
        data: '{}',
      };
      let msg = {
        type: 'send2room',
        toUser: this.onLineData.decviceCode,
        msg: JSON.stringify(data),
      };
      socket.sendMessage(msg);
    }
    // 清理键盘事件监听
    document.removeEventListener('keydown', this.handleKeydown);
    // 恢复body滚动
    document.body.style.overflow = '';
  },
  methods: {
    /**
     * @name 启动应用
    */
    startApp(){
      this.setTipType("");
      this.showUploadType = "scan";
      const url = `c30scan://?roomId=${this.roomId}&type=1`;
      window.location.href = url;
      this.isLoading = true;
      let timer = setInterval(() => {
        if (this.onLineData != null) {
          clearInterval(timer);
          timer = null;
          this.isLoading = false;
        }
      }, 1000);
      timer && setTimeout(() => {
        clearInterval(timer);
        if (this.onLineData == null) {
          this.setTipType(this.TIPTYPE.UN_INSTALL);
          this.isLoading = false;
        }
      }, 12000);
    },
    /**
     * @name 开始扫描
    */
    startScan(){
      let data = {
          type: 'startScan',
          data: '',
        };
        let msg = {
          type: 'send2room',
          toUser: this.onLineData.decviceCode,
          msg: JSON.stringify(data),
        };
        socket.sendMessage(msg);
        // this.setTipType(this.TIPTYPE.SCANING);
    },
    sendChangePage(){
      if (!this.onLineData) return;
      let data = {
        type: 'changePage',
        data: JSON.stringify({pageNum: this.thirdCardInfo.pageType}),
      };
      let msg = {
          type: 'send2room',
          toUser: this.onLineData.decviceCode,
          msg: JSON.stringify(data),
        };
      socket.sendMessage(msg);
    },
    // // 发送握手
    // sendShakeHands(decviceCode) {
    //   let roomId = {
    //     roomId: this.roomId,
    //   };
    //   let data = {
    //     type: 'shakeHands',
    //     data: JSON.stringify(roomId),
    //   };
    //   let msg = {
    //     type: 'send2room',
    //     toUser: decviceCode,
    //     msg: JSON.stringify(data),
    //   };
    //   socket.sendMessage(msg);
    // },
    // 监听客户端上线
    scanClientOnline(msg) {
      this.onLineData = JSON.parse(msg.data);
      this.sendChangePage();
      // this.$localSave.set('decviceCode', this.onLineData.decviceCode);
      // socket.joinRoom(this.onLineData.decviceCode);
      // this.setTipType(this.TIPTYPE.APP_START);
    },
    //监听客户端下线
    scanClientOffline() {
      this.onLineData = null;
      this.setTipType("");
    },
    // // 监听扫描仪链接成功
    deviceConnectioned(){
      // this.setTipType(this.TIPTYPE.SCAN_CONNECT_SUCCESS);
    },
    //设置提示类型
    setTipType(type){
      this.tipType = type;
    },
    // // 监听客户端准备就绪
    // clientReady(msg) {
    //   this.readyStatus = msg;
    //   console.log('客户端准备就绪', this.readyStatus);
    //   if (this.readyStatus != null) {
    //     this.reConnectionHandle(this.decviceCode);
    //   }
    // },
    // //客户端关闭
    // clientClose(msg) {
    //   this.closeStatus = msg;
    //   console.log('客户端关闭', this.closeStatus);
    //   if (this.closeStatus != null) {
    //     this.startClient();
    //   }
    // },
    // //启动客户端
    // startClient() {
    //   let data = {
    //     type: 'startClient',
    //     data: JSON.stringify({type:1}),
    //   };
    //   let sendMsg = {
    //     type: 'send2room',
    //     toUser: this.decviceCode,
    //     msg: JSON.stringify(data),
    //   };
    //   socket.sendMessage(sendMsg);
    // },
    // //重新唤起客户端
    // reConnectionHandle(decviceCode) {
    //   let roomId = {
    //     roomId: this.roomId,
    //     type:1
    //   };
    //   let data = {
    //     type: 'reConnectionHandle',
    //     data: JSON.stringify(roomId),
    //   };
    //   let msg = {
    //     type: 'send2room',
    //     toUser: decviceCode,
    //     msg: JSON.stringify(data),
    //   };
    //   socket.sendMessage(msg);
    // },
    // 扫描上传图片
    scanThirdCardSuccess(msg) {
      this.filePathList = [];
      this.srcList = [];
      this.imageRotations = {};
      let data = JSON.parse(msg.data);
      let paths = JSON.parse(data.paths);
      paths.forEach((item) => {
        const imageId = generateUUID();
        this.filePathList.push({
          id: imageId,
          url: item,
        });
        this.srcList.push(item);
        // 初始化旋转角度为0
        this.$set(this.imageRotations, imageId, 0);
      })
      // this.setTipType(this.TIPTYPE.APP_START);
    },
    handleClose() {
      this.$emit('close-dialog');
      this.resetData();
    },
    resetData() {
      this.filePathList = [];
      this.imageRotations = {};
      this.thirdCardInfo = {
        size: 'A3',
        pageType: '2', //0:双面 1:单面
        pageLayout: IPAGELAYOUT.A3, //：1 A4单栏  2：A3两栏  3：A3三栏 4:正3反2 5:正2反3
      };
    },
    /**
     * @name: 获取图片的旋转角度
     */
    getImageRotation(imageId) {
      return this.imageRotations[imageId] || 0;
    },
    /**
     * @name: 获取原始图片URL（去除旋转参数）
     */
    getOriginalImageUrl(url) {
      return url.replace(/\?.*$/, '');
    },
    /**
     * @name: 打开预览
     */
    openPreview(index) {
      this.currentPreviewIndex = index;
      this.previewVisible = true;
      this.previewScale = 1;
      // 阻止body滚动
      document.body.style.overflow = 'hidden';
      // 添加键盘事件监听
      document.addEventListener('keydown', this.handleKeydown);
    },
    /**
     * @name: 关闭预览
     */
    closePreview() {
      this.previewVisible = false;
      // 恢复body滚动
      document.body.style.overflow = '';
      // 移除键盘事件监听
      document.removeEventListener('keydown', this.handleKeydown);
    },
    /**
     * @name: 上一张图片
     */
    prevImage() {
      if (this.currentPreviewIndex > 0) {
        this.currentPreviewIndex--;
        this.resetView();
      }
    },
    /**
     * @name: 下一张图片
     */
    nextImage() {
      if (this.currentPreviewIndex < this.filePathList.length - 1) {
        this.currentPreviewIndex++;
        this.resetView();
      }
    },
    /**
     * @name: 放大
     */
    zoomIn() {
      this.previewScale = Math.min(this.previewScale * 1.2, 5);
    },
    /**
     * @name: 缩小
     */
    zoomOut() {
      this.previewScale = Math.max(this.previewScale / 1.2, 0.1);
    },
    /**
     * @name: 重置视图
     */
    resetView() {
      this.previewScale = 1;
    },
    /**
     * @name: 向右旋转预览图片
     */
    rotatePreviewImageRight() {
      const currentImage = this.filePathList[this.currentPreviewIndex];
      const currentRotation = this.getImageRotation(currentImage.id);
      const newRotation = (currentRotation + 90) % 360;
      this.$set(this.imageRotations, currentImage.id, newRotation);
    },
    /**
     * @name: 向左旋转预览图片
     */
    rotatePreviewImageLeft() {
      const currentImage = this.filePathList[this.currentPreviewIndex];
      const currentRotation = this.getImageRotation(currentImage.id);
      const newRotation = (currentRotation - 90 + 360) % 360;
      this.$set(this.imageRotations, currentImage.id, newRotation);
    },
    /**
     * @name: 鼠标滚轮缩放
     */
    handleWheel(event) {
      event.preventDefault();
      if (event.deltaY < 0) {
        this.zoomIn();
      } else {
        this.zoomOut();
      }
    },

    /**
     * @name: 获取图片最大尺寸
     */
    getImageMaxSize() {
      const rotation = this.getImageRotation(this.filePathList[this.currentPreviewIndex]?.id);
      const isRotated = rotation === 90 || rotation === 270;

      return {
        width: isRotated ? '80vh' : '80vw',
        height: isRotated ? '80vw' : '80vh'
      };
    },
    /**
     * @name: 键盘事件处理
     */
    handleKeydown(event) {
      if (!this.previewVisible) return;

      switch (event.key) {
        case 'Escape':
          this.closePreview();
          break;
        case 'ArrowLeft':
          event.preventDefault();
          this.prevImage();
          break;
        case 'ArrowRight':
          event.preventDefault();
          this.nextImage();
          break;
        case ' ':
          event.preventDefault();
          this.rotatePreviewImageRight();
          break;
        case 'r':
        case 'R':
          event.preventDefault();
          this.rotatePreviewImageRight();
          break;
        case 'l':
        case 'L':
          event.preventDefault();
          this.rotatePreviewImageLeft();
          break;
        case '+':
        case '=':
          event.preventDefault();
          this.zoomIn();
          break;
        case '-':
          event.preventDefault();
          this.zoomOut();
          break;
        case '0':
          event.preventDefault();
          this.resetView();
          break;
      }
    },
    /**
     * @name: 确定创建三方卡
     */
    confirmCreate() {
      if (this.filePathList.length == 0) {
        this.$message.warning('请上传答题卡');
        return;
      }
      if(this.thirdCardInfo.pageType == 1 && this.filePathList.length > 1){
        this.$message.warning('单面仅支持一张图片');
        return;
      }
      if (this.thirdCardInfo.pageType == 2 && this.filePathList.length != 2) {
        this.$message.warning('请上传两张图片');
        return;
      }
      if (this.thirdCardInfo.pageType == 3 && this.filePathList.length != 3) {
        this.$message.warning('请上传三张图片');
        return;
      }
      if (this.thirdCardInfo.pageType == 4 && this.filePathList.length != 4) {
        this.$message.warning('请上传四张图片');
        return;
      }
      let imgUrl = this.filePathList.map((item, index) => {
         let hasQuestionMark = /\?.*/.test(item.url);
         let ate = this.getImageRotation(item.id);
          if (hasQuestionMark) {
            let replacedUrl = item.url.replace(/\?.*$/, '');
            item.url = replacedUrl;
          }
          if(ate != 0){
            item.url = item.url + `?x-oss-process=image/rotate,${ate}`;
          }
        return {
          page: index + 1,
          url: item.url,
        };
      });
      this.$emit('confirm-create', {
        thirdCardInfo: this.thirdCardInfo,
        imgUrl: imgUrl,
      });
      this.resetData();
    },
    async getPdfImage(file) {
      this.$loading({ lock: true, text: '上传中...', background: 'rgba(0, 0, 0, 0.7)' });
      let formData = new FormData();
      formData.append('file', file);
      try {
        let res = await getPdfImage(formData);
        if (res.code == 1) {
          this.showUploadType = "upload";
          res.data.images.forEach((item) => {
            const imageId = generateUUID();
            this.filePathList.push({
              id: imageId,
              url: item,
            });
            this.srcList.push(item);
            // 初始化旋转角度为0
            this.$set(this.imageRotations, imageId, 0);
          });
        } else {
          this.$message.error('上传失败');
        }
      } catch (error) {
        this.$message.error('上传失败');
      }
      this.$loading().close();
    },

    /**
     * word上传前
     */
     async beforeUpload(file) {
      let promise = new Promise(async (resolve, reject) => {
        if(file.size > 20*1024*1024 ){
          this.$message.error('文件大小不能超过20MB');
          reject()
          return;
        }
        if(!this.coverAccept.split(',').includes(get_suffix(file.name))){
          this.$message.error('文件格式不支持，请选择'+this.coverAccept+'格式的文档');
          reject()
          return;
        }
        let path = this.getDateForPath();
        await ossUploadFile.getSTSToken(path);
        resolve(true);
        });
      return promise; // 通过返回一个promis对象解决
    },
    ossUpload(data){
      return new Promise((resolve, reject) => {
        if (data.file.type.indexOf('pdf') >= 0) {
          this.getPdfImage(data.file)
        } else {
          let path = this.getDateForPath() + guid() + '/f' + get_suffix(data.file.name);
          ossUploadFile.uploadFile(data.file, path, (res) => {
            if (res.code == 1) {
              resolve(res.res)
            } else {
              this.$message.error('上传失败')
              reject(res)
            }
          })
        }
      });
    },
    /**
     * 上传成功回调
     */
    uploadSuccess(response) {
      this.showUploadType = "upload";
      const imageId = generateUUID();
      this.filePathList.push({
        id: imageId,
        url: this.fsUrl + '/' + response.name,
      });
      this.srcList.push(this.fsUrl + '/' + response.name);
      // 初始化旋转角度为0
      this.$set(this.imageRotations, imageId, 0);
    },
    /**
     * 根据当前时间当前用户的学校id和用户id拼接文件夹路径
     * @returns {string}
     */
     getDateForPath() {
      let date = new Date()
      let y = date.getFullYear()
      let m = date.getMonth() + 1
      m = m < 10 ? ('0' + m) : m
      let d = date.getDate()
      d = d < 10 ? ('0' + d) : d
      // 当用户学校id不为空时
      return this.filePath + '/' + y + '/' + m + '/' + d + '/' ;
    },
    /**
     * word文件太大的提示
     */
    handleMaxSize(file) {
      this.$Message.error('文件' + file.name + '太大，不允许超过20M');
    },
    /**
     * 上传的文件格式验证失败的提示
     */
    handleFormat(file) {
      this.$Message.error('文件' + file.name + '格式不支持，请选择jpg,png,pdf格式的文档');
    },
    /**
     * @name:交换图片顺序
     */
    handleExchange(data) {
      let index = this.filePathList.findIndex(item => item.id == data.id);
      let index2 = this.filePathList.findIndex(item => item.id != data.id);
          // 保存原始数据
      const item1 = this.filePathList[index];
      const item2 = this.filePathList[index2];

      // 使用$set确保响应式更新
      this.$set(this.filePathList, index, item2);
      this.$set(this.filePathList, index2, item1);

      // 更新srcList
      this.srcList = this.filePathList.map(item => item.url);
    },
    /**
     * @name:旋转图片
     */
    handleRotate(item) {
      const currentRotation = this.getImageRotation(item.id);
      const newRotation = (currentRotation + 90) % 360;
      this.$set(this.imageRotations, item.id, newRotation);
    },
    /**
     * @name:删除图片
     */
    handleRemove(data) {
      this.filePathList = this.filePathList.filter(item => item.id != data.id);
      this.srcList = this.filePathList.map(item => {
        return item.url;
      });
      // 删除对应的旋转状态
      this.$delete(this.imageRotations, data.id);
    },
  },
};
</script>

<style lang="scss" scoped>
.tip {
  color: #fd032f;
}
.upload-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
.upload {
  width: 100px;
  height: 100px;
  background: #fff;
    border: 1px dashed #dcdee2;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: border-color .2s ease;
    margin-right: 10px;
    &:hover{
      border: 1px dashed #2d8cf0;
    }
    .icon-scan{
      height: 32px;
      width: 32px;
      margin: 0 auto;
      background: url("../../../../assets/scan/scan-upload.png");
    }
  .icon-photo{
    height: 32px;
      width: 32px;
      margin: 0 auto;
      background: url("../../../../assets/scan/photo-upload.png");
  }
}
.disabledUpload {
  cursor: not-allowed;
}
.img-list {
  height: 200px;
  width: 200px;
  margin-right: 50px;
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  background: #fff;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  &.more{
    margin-right: 10px;
    margin-bottom: 30px;
  }
  .img-list-item {
    width: 100%;
    height: 100%;
    cursor: pointer;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      transition: transform 0.3s ease;
    }
  }
  .img-page {
    // position: absolute;
    // bottom: -15px;
    // height: 20px;
    // left: 15px;
  }
  .rotate-icon {
    position: absolute;
    bottom: 0px;
    right: 25px;
    font-size: 20px;
    color: #409eff;
  }
  .delete-icon {
    position: absolute;
    bottom: 0px;
    right: 0;
    font-size: 20px;
    color: red;
  }
  .exchange-icon {
    position: absolute;
    bottom: 100px;
    right: -30px;
    transform: rotate(90deg);
    color: #409eff;
  }
}

.upload-icon {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100px;
  height: 100px;
}

// 自定义预览组件样式
.custom-image-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;

  .preview-container {
    width: 90vw;
    height: 90vh;
    background: #fff;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e4e7ed;
    background: #f5f7fa;

    .preview-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }

    .preview-close {
      font-size: 20px;
      cursor: pointer;
      color: #909399;
      transition: color 0.3s;

      &:hover {
        color: #409eff;
      }
    }
  }

  .preview-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .preview-controls {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;

    .el-icon-arrow-left,
    .el-icon-arrow-right {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      font-size: 24px;
      color: #409eff;
      cursor: pointer;
      z-index: 10;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s;

      &:hover {
        background: rgba(64, 158, 255, 0.1);
        color: #66b1ff;
      }
    }

    .el-icon-arrow-left {
      left: 20px;
    }

    .el-icon-arrow-right {
      right: 20px;
    }
  }

  .preview-image-container {
    height: 100%;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
  }

  .preview-image {
    transition: transform 0.3s ease;
    user-select: none;
    object-fit: contain;
  }

  .preview-toolbar {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
    border-top: 1px solid #e4e7ed;
    background: #f5f7fa;
    gap: 20px;

    i {
      font-size: 18px;
      color: #606266;
      cursor: pointer;
      padding: 8px;
      border-radius: 4px;
      transition: all 0.3s;

      &:hover {
        color: #409eff;
        background: rgba(64, 158, 255, 0.1);
      }
    }

    .zoom-text {
      font-size: 14px;
      color: #606266;
      min-width: 50px;
      text-align: center;
    }
  }
}
</style>
<style lang="scss">
.disabledUpload {
  .ivu-upload-drag {
    cursor: not-allowed;
  }
  .ivu-upload-drag:hover {
    border: 1px dashed #dcdee2;
  }
}
</style>