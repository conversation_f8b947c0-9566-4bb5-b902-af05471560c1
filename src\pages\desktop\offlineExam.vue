<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-08-28 16:09:33
 * @LastEditors: 小圆
-->
<template>
  <div class="offline-exam">
    <div class="offline-exam-module offline-exam-top">
      <div class="exam-card question-card" @click="goQuestion"></div>
      <div class="exam-card card-card" @click="goCard"></div>
      <div class="exam-card correct-card" @click="goCorrect"></div>
      <div class="exam-card report-card" @click="goReport"></div>
      <div class="exam-card more-card">
        <div class="more-card-title"><span class="label"> 更多 </span></div>

        <div class="more-card-content">
          <div class="more-card-item" @click="goCreateExam">
            <div class="more-card-icon crate-icon"></div>
            <div class="more-card-label">新键考试</div>
          </div>
          <div class="more-card-item" @click="goReuseExam">
            <div class="more-card-icon reuse-icon"></div>
            <div class="more-card-label">复用考试</div>
          </div>
          <div class="more-card-item" @click="goManageExam">
            <div class="more-card-icon manage-icon"></div>
            <div class="more-card-label">管理考试</div>
          </div>
          <div class="more-card-item" @click="goWrongQues">
            <div class="more-card-icon wrongques-icon"></div>
            <div class="more-card-label">班级错题</div>
          </div>
        </div>
      </div>
    </div>

    <OfflineExamList class="offline-exam-module" :defaultCategory="[2, 3, 4, 5, 6, 7, 8, 9, 10, 11]"></OfflineExamList>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import OfflineExamList from './components/offlineExamList.vue';
import { getPublicConfigBySchoolInfo } from '@/service/api';
import { openRoute, showWarningMessage } from './desktopUtils';
import './message.scss';

@Component({
  components: {
    OfflineExamList,
  },
})
export default class OfflineExam extends Vue {
  schoolQuesModuleEnabled = false;

  mounted() {
    this.getSchoolConfig();
  }

  // 获取学校中控配置
  async getSchoolConfig() {
    let code = [
      '009', // 校本题库, 班级错题， 我的卷库
    ];
    try {
      const res = await getPublicConfigBySchoolInfo({
        schoolId: this.$sessionSave.get('schoolInfo').id,
        userId: this.$sessionSave.get('loginInfo').id,
        dictCode: code.join(','),
      });
      res.data.forEach(item => {
        // 校本题库
        if (item.dictCode == '009') {
          this.schoolQuesModuleEnabled = item.state == '1';
        }
      });
    } catch (error) {
      console.error(error);
    }
  }

  goQuestion() {
    if (this.schoolQuesModuleEnabled) {
      openRoute('/home/<USER>/school', {
        'paper-tab': 'school',
      });
    } else {
      showWarningMessage('尚未开通此服务，请联系商务开通');
    }
  }
  goCard() {
    openRoute('/home/<USER>', {
      pageType: 'cardTool',
    });
  }
  goCorrect() {
    openRoute('/home/<USER>', {
      pageType: 'checkTask',
    });
  }
  goReport() {
    openRoute('/home/<USER>');
  }
  goCreateExam() {
    openRoute('/home/<USER>', {
      operation: 'create',
    });
  }
  goReuseExam() {
    openRoute('/home/<USER>', {
      operation: 'reuse',
    });
  }
  goManageExam() {
    openRoute('/home/<USER>');
  }
  goWrongQues() {
    openRoute('/home/<USER>/wrongques', {
      'paper-tab': 'wrongques',
    });
  }
}
</script>

<style scoped lang="scss">
.offline-exam {
}

.offline-exam-module {
  padding: 20px;
  background-color: #fff;
  box-shadow: 0px 4px 4px 0px rgba(217, 222, 232, 0.25);
  border-radius: 12px;
  margin-bottom: 10px;

  &:last-of-type {
    margin-bottom: 0;
  }
}

.offline-exam-top {
  display: flex;

  .exam-card {
    width: 0;
    flex: 1;
    max-width: 248px;
    min-width: 220px;
    height: 170px;
    margin-right: 20px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    border-radius: 12px;
    cursor: pointer;

    &:hover {
      box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.1);
    }

    &:last-of-type {
      margin-right: 0;
    }

    &.question-card {
      background-image: url('~@/assets/desktop/exam_question_banner.png');
    }

    &.card-card {
      background-image: url('~@/assets/desktop/exam_card_banner.png');
    }

    &.correct-card {
      background-image: url('~@/assets/desktop/exam_correct_banner.png');
    }

    &.report-card {
      background-image: url('~@/assets/desktop/exam_report_banner.png');
    }

    &.more-card {
      background: linear-gradient(199deg, #f5f9ff 0%, #ffffff 34%, #f5f9fb 91%);
      padding: 12px;
    }
  }
}

.more-card {
  .more-card-title {
    margin-bottom: 10px;
  }
  .more-card-content {
    display: flex;
    flex-wrap: wrap;
  }

  .more-card-item {
    width: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 10px;
    cursor: pointer;

    &:hover {
      .more-card-label {
        color: #2574ff;
      }

      .crate-icon {
        background-image: url('~@/assets/desktop/exam_create_active_icon.png');
      }
      .reuse-icon {
        background-image: url('~@/assets/desktop/exam_reuse_active_icon.png');
      }
      .manage-icon {
        background-image: url('~@/assets/desktop/exam_manage_active_icon.png');
      }
      .wrongques-icon {
        background-image: url('~@/assets/desktop/exam_wrongques_active_icon.png');
      }
    }

    .more-card-label {
      font-weight: 400;
      font-size: 16px;
      color: #7b7a7e;
    }

    .more-card-icon {
      width: 24px;
      height: 24px;
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .crate-icon {
      background-image: url('~@/assets/desktop/exam_create_icon.png');
    }
    .reuse-icon {
      background-image: url('~@/assets/desktop/exam_reuse_icon.png');
    }
    .manage-icon {
      background-image: url('~@/assets/desktop/exam_manage_icon.png');
    }
    .wrongques-icon {
      background-image: url('~@/assets/desktop/exam_wrongques_icon.png');
    }
  }
}

.label {
  display: flex;
  align-items: center;
  font-weight: 700;
  font-size: 18px;
  color: #000000;

  &::before {
    display: inline-block;
    content: '';
    width: 4px;
    height: 18px;
    background: #1e6ffe;
    border-radius: 2px;
    margin-right: 8px;
  }
}
</style>
