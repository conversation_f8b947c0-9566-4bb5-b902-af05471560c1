<template>
  <el-dialog title="导出" :visible.sync="dialogVisible" width="620px" v-on="$listeners">
    <div class="card-item">
      <div class="card-item-header">
        <p class="titleLine">班级成绩单(EXCEL)</p>
      </div>

      <!-- 指标筛选 -->
      <div v-if="indicatorList.length > 0" class="exprot-box">
        <el-checkbox
          style="margin-bottom: 15px"
          :indeterminate="indicator.isIndeterminate"
          v-model="indicator.checkAll"
          @change="handleIndicatorCheckAllChange"
          >全选</el-checkbox
        >
        <el-checkbox-group
          class="checkbox-type-group"
          v-model="indicator.checkList"
          @change="handleIndicatorCheckedChange"
        >
          <el-checkbox
            class="checkbox"
            v-for="item in indicatorList"
            :label="item.value"
            :key="item.value"
            :disabled="item.disabled"
            >{{ item.label }}</el-checkbox
          >
        </el-checkbox-group>
      </div>
      <div style="text-align: right">
        <el-button class="card-button" type="primary" @click="exportScoreCard">导出</el-button>
      </div>
    </div>

    <div class="card-item">
      <p class="titleLine">学生个人成绩单</p>

      <div style="margin-bottom: 15px">
        <el-radio-group v-model="docType">
          <el-radio v-for="item in docTypeOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
        </el-radio-group>
      </div>

      <div style="margin-bottom: 15px">
        <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleStuCheckAllChange"
          >全选</el-checkbox
        >
      </div>

      <div class="checkbox-box">
        <!-- 分数指标 -->
        <el-checkbox-group
          class="checkbox-group"
          v-model="stuCheckOption.score.checkList"
          @change="updateStuCheckAllStatus"
        >
          <el-checkbox
            v-for="item in stuCheckOption.score.list"
            :disabled="item.disabled"
            :label="item.value"
            :key="item.value"
            >{{ item.label }}</el-checkbox
          >
        </el-checkbox-group>

        <!-- 赋分指标 -->
        <el-checkbox-group
          class="checkbox-group"
          v-if="stuCheckOption.rule.list.length > 0"
          v-model="stuCheckOption.rule.checkList"
          @change="updateStuCheckAllStatus"
        >
          <el-checkbox
            v-for="item in stuCheckOption.rule.list"
            :disabled="item.disabled"
            :label="item.value"
            :key="item.value"
            >{{ item.label }}</el-checkbox
          >
        </el-checkbox-group>

        <!-- 班级指标 -->
        <div>
          <el-checkbox-group
            v-if="stuCheckOption.cls.list.length > 0"
            v-model="stuCheckOption.cls.checkList"
            @change="updateStuCheckAllStatus"
          >
            <el-checkbox
              v-for="item in stuCheckOption.cls.list"
              :disabled="item.disabled"
              :label="item.value"
              :key="item.value"
              >{{ item.label }}</el-checkbox
            >
          </el-checkbox-group>

          <!-- 年级指标 -->
          <el-checkbox-group
            v-if="stuCheckOption.grd.list.length > 0"
            v-model="stuCheckOption.grd.checkList"
            @change="updateStuCheckAllStatus"
          >
            <el-checkbox
              v-for="item in stuCheckOption.grd.list"
              :disabled="item.disabled"
              :label="item.value"
              :key="item.value"
              >{{ item.label }}</el-checkbox
            >
          </el-checkbox-group>
        </div>
      </div>

      <div style="text-align: right">
        <el-button class="card-button" type="primary" @click="exportStuScoreCard">导出</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import UserRole from '@/utils/UserRole';
import { Component, Prop, Vue } from 'vue-property-decorator';

const scoreOptions = [
  {
    label: '满分',
    value: 'full',
    disabled: false,
  },
  {
    label: '得分',
    value: 'score',
    disabled: false,
  },
  {
    label: '等级',
    value: 'lv',
    disabled: false,
  },
  {
    label: '小题明细',
    value: 'qs',
    disabled: false,
  },
];

const ruleOptions = [
  {
    label: '赋分',
    value: 'rule',
    disabled: false,
  },
  {
    label: '赋分等级',
    value: 'ruleLv',
    disabled: false,
  },
];

const clsOptions = [
  {
    label: '班级排名',
    value: 'rank',
    disabled: false,
  },
  {
    label: '班级最高分',
    value: 'max',
    disabled: false,
  },
  {
    label: '班级平均分',
    value: 'avg',
    disabled: false,
  },
];

const grdOptions = [
  {
    label: '年级排名',
    value: 'rank',
    disabled: false,
  },
  {
    label: '年级最高分',
    value: 'max',
    disabled: false,
  },
  {
    label: '年级平均分',
    value: 'avg',
    disabled: false,
  },
];

const docTypeOptions = [
  {
    label: 'PDF',
    value: 'pdf',
    url: '/phelp/_/exp-stu-card-pdf',
  },
  {
    label: 'EXCEL',
    value: 'excel',
    url: '/pexam/_/exportStuCard',
  },
];

@Component
export default class ReportExportDialog extends Vue {
  // 筛选数据
  @Prop() filterData: any;
  // 对比考试id
  @Prop({ default: '' }) withExamId: string;
  // 是否赋分
  @Prop({ default: false }) isRule: boolean;
  // 是否显示年级排名
  @Prop({ default: true }) isGrdRankEnable: boolean;
  // 是否显示班级排名
  @Prop({ default: true }) isClsRankEnable: boolean;
  // 只显示等级
  @Prop({ default: 0 }) onlyLv;
  @Prop({ type: Array, default: () => [] }) indicatorList: any[];
  @Prop({ type: Array, default: () => [] }) defaultIndicators: any[];

  dialogVisible: boolean = true;

  // 指标筛选
  indicator = {
    list: [],
    checkList: [],
    isIndeterminate: true,
    checkAll: false,
  };

  // 文档类型选项
  docTypeOptions = docTypeOptions;
  // 当前文档类型
  docType = docTypeOptions[0].value;

  // 是否全选
  checkAll = false;
  // 是否不确定选择
  isIndeterminate = true;

  // 指标选择
  stuCheckOption = {
    score: {
      list: [],
      checkList: [],
    },
    rule: {
      list: [],
      checkList: [],
    },
    cls: {
      list: [],
      checkList: [],
    },
    grd: {
      list: [],
      checkList: [],
    },
  };

  mounted() {
    this.initStuCheckOption();
    this.initIndicator();
  }

  // 初始化学神个人成绩单指标
  initStuCheckOption() {
    // 初始化分数指标
    this.stuCheckOption.score.list = scoreOptions.map(item => ({ ...item, disabled: false }));
    // 初始化赋分指标 非赋分则不显示
    this.stuCheckOption.rule.list = this.isRule ? ruleOptions.map(item => ({ ...item, disabled: false })) : [];
    // 初始化班级指标，根据是否启用班排功能权限判断是否禁用
    this.stuCheckOption.cls.list = clsOptions.map(item => {
      let disabled = false;
      if (item.value == 'rank') {
        disabled = !this.isClsRankEnable;
      }
      return { ...item, disabled };
    });
    // 初始化年级指标，根据是否启用年排权限判断是否禁用
    this.stuCheckOption.grd.list = grdOptions.map(item => {
      let disabled = false;
      if (item.value == 'rank') {
        disabled = !this.isGrdRankEnable;
      }
      return { ...item, disabled };
    });
    // 如果只显示等级，则只保留等级指标
    if (this.onlyLv) {
      this.stuCheckOption.cls.list = [];
      this.stuCheckOption.grd.list = [];
      this.stuCheckOption.rule.list = [];
      this.stuCheckOption.score.list = scoreOptions
        .filter(item => item.value == 'lv')
        .map(item => ({ ...item, disabled: false }));
    }
    // 默认选中分数和赋分指标
    this.stuCheckOption.score.checkList = this.stuCheckOption.score.list.map(item => item.value);
    this.stuCheckOption.rule.checkList = this.stuCheckOption.rule.list.map(item => item.value);
    this.stuCheckOption.cls.checkList = [];
    this.stuCheckOption.grd.checkList = [];
    // 更新全选状态
    this.updateStuCheckAllStatus();
  }

  // 初始化班级成绩单指标
  initIndicator() {
    // 初始化指标列表
    if (this.indicatorList.length > 0) {
      this.indicator.list = this.indicatorList;
      if (this.defaultIndicators.length > 0) {
        // 过滤掉disabled的默认指标
        this.indicator.checkList = this.defaultIndicators.filter(defaultValue => {
          const item = this.indicator.list.find(listItem => listItem.value === defaultValue);
          return item && !item.disabled;
        });
      } else {
        // 只选择未被禁用的指标
        this.indicator.checkList = this.indicator.list.filter(item => !item.disabled).map(item => item.value);
      }
      // 计算可选择的指标数量（排除disabled的）
      const enabledCount = this.indicator.list.filter(item => !item.disabled).length;
      this.indicator.checkAll = this.indicator.checkList.length === enabledCount;
    }
  }

  // 导出成绩单
  async exportScoreCard() {
    if (this.indicator.checkList.length == 0 && !this.onlyLv) {
      this.$message.warning('请先勾选指标！');
      return;
    }
    let role = '';
    if (!UserRole.isOperation) {
      const { year, campusCode } = this.$sessionSave.get('reportDetail');
      const map = await UserRole.utils.getRoleSubjectClassMap(
        year,
        campusCode,
        this.$sessionSave.get('reportType') == 'school' ? true : false
      );
      role = JSON.stringify(map);
    }
    const params: any = {
      examId: this.$sessionSave.get('reportDetail').examId,
      role: role,
      source: this.filterData.source,
      withExamId: this.withExamId,
      abPaper: this.filterData.abPaper,
      v: this.$sessionSave.get('reportDetail').v,
    };
    if (this.onlyLv) {
      params.filter = 'lv';
    } else {
      params.filter = this.indicator.checkList.filter(item => this.indicatorList.some(t => t.value == item)).join(',');
    }

    const urlSearch = new URLSearchParams(params);
    let url = process.env.VUE_APP_KKLURL + `/pexam/_/exportScoreCard?${urlSearch.toString()}`;
    window.open(url);
  }

  // 导出学生成绩单
  async exportStuScoreCard() {
    // 检查是否有选中的指标
    const totalChecked =
      this.stuCheckOption.score.checkList.length +
      this.stuCheckOption.rule.checkList.length +
      this.stuCheckOption.cls.checkList.length +
      this.stuCheckOption.grd.checkList.length;

    if (totalChecked === 0) {
      this.$message.warning('请先勾选指标！');
      return;
    }

    let card = {
      '': [],
      cls: [],
      grd: [],
    };

    // 组合分数和赋分指标
    card[''] = [...this.stuCheckOption.score.checkList, ...this.stuCheckOption.rule.checkList];
    card.cls = this.stuCheckOption.cls.checkList;
    card.grd = this.stuCheckOption.grd.checkList;

    let apiUrl = docTypeOptions.find(item => item.value == this.docType).url;

    const params = {
      examId: this.$sessionSave.get('reportDetail').examId,
      subjectId: this.filterData.subjectId,
      clzId: this.filterData.classId,
      card: JSON.stringify(card),
      abPaper: this.filterData.abPaper,
      v: this.$sessionSave.get('reportDetail').v,
    };
    const urlSearch = new URLSearchParams(params as any);
    let url = process.env.VUE_APP_KKLURL + apiUrl + `?${urlSearch.toString()}`;
    window.open(url);
  }

  updateStuCheckAllStatus() {
    // 计算所有已选择的项目数量
    const totalChecked =
      this.stuCheckOption.score.checkList.length +
      this.stuCheckOption.rule.checkList.length +
      this.stuCheckOption.cls.checkList.length +
      this.stuCheckOption.grd.checkList.length;

    // 计算所有可选择的项目数量（排除disabled的）
    const totalEnabled =
      this.stuCheckOption.score.list.filter(item => !item.disabled).length +
      this.stuCheckOption.rule.list.filter(item => !item.disabled).length +
      this.stuCheckOption.cls.list.filter(item => !item.disabled).length +
      this.stuCheckOption.grd.list.filter(item => !item.disabled).length;

    this.checkAll = totalChecked === totalEnabled;
    this.isIndeterminate = totalChecked > 0 && totalChecked < totalEnabled;
  }

  handleStuCheckAllChange(val) {
    if (val) {
      // 全选：选择所有未禁用的项目
      this.stuCheckOption.score.checkList = this.stuCheckOption.score.list
        .filter(item => !item.disabled)
        .map(item => item.value);
      this.stuCheckOption.rule.checkList = this.stuCheckOption.rule.list
        .filter(item => !item.disabled)
        .map(item => item.value);
      this.stuCheckOption.cls.checkList = this.stuCheckOption.cls.list
        .filter(item => !item.disabled)
        .map(item => item.value);
      this.stuCheckOption.grd.checkList = this.stuCheckOption.grd.list
        .filter(item => !item.disabled)
        .map(item => item.value);
    } else {
      // 取消全选
      this.stuCheckOption.score.checkList = [];
      this.stuCheckOption.rule.checkList = [];
      this.stuCheckOption.cls.checkList = [];
      this.stuCheckOption.grd.checkList = [];
    }
    this.isIndeterminate = false;
  }

  // 指标全选切换
  handleIndicatorCheckAllChange(value) {
    // 只选择未被禁用的指标
    this.indicator.checkList = value ? this.indicator.list.filter(item => !item.disabled).map(item => item.value) : [];
    this.indicator.isIndeterminate = false;
  }

  // 指标选择改变
  handleIndicatorCheckedChange(value) {
    let checkedCount = value.length;
    this.indicator.checkList = value;
    let enabledCount = this.indicator.list.filter(item => !item.disabled).length;
    this.indicator.checkAll = checkedCount === enabledCount;
    this.indicator.isIndeterminate = checkedCount > 0 && checkedCount < enabledCount;
  }
}
</script>

<style lang="scss" scoped>
.card-item {
  &:not(:last-of-type) {
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: 20px;
  }

  .card-item-header {
    display: flex;
    justify-content: space-between;
  }
}
.titleLine {
  position: relative;
  display: flex;
  align-items: center;

  margin-bottom: 10px;

  line-height: 32px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;

  &::before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    margin-right: 10px;
  }
}
.card-button {
  margin-top: 5px;
}

.exprot-box {
  margin-bottom: 20px;
}

.titleLine-small {
  display: inline-block;
  position: relative;
  height: 32px;
  line-height: 32px;
  font-size: 14px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 12px;

  &:before {
    content: '';
    width: 4px;
    height: 16px;
    background: #409eff;
    border-radius: 2px;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
  }
}

.checkbox-type-group {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  column-gap: 10px;
  row-gap: 10px;
}

.checkbox-box {
  display: flex;

  .checkbox-group {
    width: 120px;
  }

  ::v-deep .el-checkbox {
    margin-bottom: 5px;
  }
}
</style>
