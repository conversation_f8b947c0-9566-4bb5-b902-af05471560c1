<template>
  <div class="essay-setting-container">
    <el-form ref="form" :model="form" label-width="100px" :rules="rules">
      <el-form-item label="选择题号" prop="quesNo">
        <template v-for="item in aiEssayQuesData">
          <el-button v-if="!item.hasTip" class="btn-item ellipsis-btn" :key="item.id" @click="changeQuesNo(item)"
            :type="form.ques.quesNo == item.quesNo ? 'primary' : 'default'">{{ item.quesNos
            }}</el-button>
          <el-badge v-else is-dot class="btn-item" :key="'dot_' + item.id"><el-button class="ellipsis-btn"
              :key="item.id" @click="changeQuesNo(item)"
              :type="form.ques.quesNo == item.quesNo ? 'primary' : 'default'">{{ item.quesNos
              }}</el-button></el-badge>
        </template>
      </el-form-item>

      <el-form-item v-if="isEnglishSubject" prop="style">
        <template slot="label">
          <span>作文体裁
            <el-popover placement="right-start" trigger="hover">
              <p>如果类型选择错误，会影响AI判分的效果，请务必慎重选择。开启作文智批阅卷后，将无法再修改作文体裁。</p>
              <i class="el-icon-warning" style="color: #F56C6C" slot="reference"></i>
            </el-popover></span>
        </template>
        <el-radio-group :disabled="isReadonly" v-model="form.way" class="essay-type-radio">
          <div class="radio-item">
            <el-radio :label="1">应用文<span class="radio-desc">通用的作文题型，要求学生按照一个文章主题写一篇短文，常见的类型有书信、邮件、记叙文、看图写作等。</span>
            </el-radio>

          </div>
          <div class="radio-item">
            <el-radio :label="2">读后续写<span class="radio-desc">常见于新高考/新中考地区的考试中，要求学生阅读已有的文章后，续写一段或者两段文字，形成一篇完整的文章。</span>
            </el-radio>

          </div>
        </el-radio-group>
      </el-form-item>

      <el-form-item class="form-block" label="作文满分">
        <div class="score-display">{{ form.score }}分</div>
      </el-form-item>

      <el-form-item v-if="isEnglishSubject" class="form-block" label="词数要求" prop="wordCount">
        <el-input-number :disabled="isReadonly" type="text" v-model="form.wordCount" :min="0" :max="9999" :step="10" :precision="0" size="small"
          placeholder="请输入词数"></el-input-number>
      </el-form-item>

      <!-- 表单内容区域容器 -->
      <div class="form-content-wrapper">
        <!-- 左侧表单区域 -->
        <div class="form-section" :class="{ 'with-image': (isEnglishSubject && form.way == 2) }">
          <el-form-item label="作文题目" prop="question">
            <el-input :disabled="isReadonly" type="textarea" v-model="form.question" rows="5" placeholder="请输入作文题目的详细要求..."
              resize="none"></el-input>
            <div class="hint-text">请输入作文题目的具体描述，包括主题、字数要求等</div>
          </el-form-item>

          <template v-if="isEnglishSubject">
            <!-- 读后续写 -->
            <template v-if="form.way == 2">
              <el-form-item label="第一段首句" prop="firstLine">
                <el-input :disabled="isReadonly" type="text" v-model="form.firstLine"
                  placeholder="例：As  I  returned  to  my  seat,  the  captain  made  an  announcement  about  Dakota   situation."></el-input>
              </el-form-item>
              <el-form-item label="第二段首句" prop="secondLine">
                <el-input :disabled="isReadonly" type="text" v-model="form.secondLine"
                  placeholder="例：Immediately, I took Dakota out ofthe box,finding he was very cold and shaking."></el-input>
              </el-form-item>
            </template>

            <el-form-item label="参考范文" prop="demo">
              <el-input :disabled="isReadonly" type="textarea" v-model="form.demo" rows="5" placeholder="请输入参考范文..."
                resize="none"></el-input>
            </el-form-item>
          </template>
        </div>

        <!-- 右侧图片显示区域 -->
        <div v-if="isEnglishSubject && form.way == 2" class="image-display-area">
          <div class="image-container">
            <div class="image-header">
              <span class="image-title">题目图片</span>
            </div>

            <!-- 图片列表 -->
            <div class="image-list" v-if="areaImgList && areaImgList.length">
              <div
                v-for="(image, index) in areaImgList"
                :key="index"
                class="image-item"
              >
                <!-- 交互式图片容器 -->
                <div class="interactive-image-container">
                  <!-- 图片显示区域 -->
                  <div
                    class="image-viewport"
                    @wheel.prevent="handleWheel($event, index)"
                    @mousedown="handleMouseDown($event, index)"
                    @mousemove="handleMouseMove($event, index)"
                    @mouseup="handleMouseUp($event, index)"
                    @mouseleave="handleMouseLeave($event, index)"
                    :style="getViewportStyle(index)"
                  >
                    <img
                      :ref="`image-${index}`"
                      :src="image"
                      :style="getImageStyle(index)"
                      @error="handleImageError($event, index)"
                      @dragstart.prevent
                      class="interactive-image"
                    />
                    <!-- 缩放比例显示 -->
                    <div v-if="imageStates[index] && imageStates[index].scale !== 1" class="scale-indicator">
                      {{ Math.round(imageStates[index].scale * 100) }}%
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>

      <el-form-item label="批改要求" prop="corrRule">
        <el-input :disabled="isReadonly" type="textarea" v-model="form.corrRule" rows="5" placeholder="请输入批改的具体标准..."
          resize="none"></el-input>
        <div class="hint-text">请详细说明批改标准，如语法、内容、结构等方面的评分要点</div>
      </el-form-item>

      <el-form-item v-if="correctType == ICORRECT_TYPES.WEB" label="得分说明" prop="type">
        <el-radio-group v-model="form.type">
          <div class="radio-item">
            <el-radio :disabled="isReadonly" :label="1"> AI 批改：AI批改，老师复核，可直接使用AI评分</el-radio>
          </div>
          <div class="radio-item">
            <el-radio :disabled="isReadonly" :label="2">人机双评：AI初评，老师二评，以老师打分为准，可设置误差分，达到误差分则由仲裁老师进行评阅打分</el-radio>
            <div style="margin-left: 92px;font-size:12px;" v-if="form.type == 2">
              <span>开启仲裁：</span><el-switch v-model="form.isArbitration" :disabled="isReadonly" />
              <template v-if="form.isArbitration">
                <span style="margin-left: 20px;">分差大于<el-input v-model="form.beyondScore" :disabled="isReadonly"
                    style="width: 48px; height: 32px"></el-input>分进入仲裁</span>
                <span style="margin-left: 20px;"><el-checkbox v-model="form.isCheckScore" size="medium"
                    :disabled="isReadonly" border>仲裁时可查看AI和阅卷老师的评分</el-checkbox></span>
              </template>
            </div>
          </div>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button :disabled="isReadonly" style="float:right;" type="primary" @click="submitForm">保存修改</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {
  getExamAiCorrectQueAPI,
  saveExamAiCorrectQueAPI
} from '@/service/api';
import { ICORRECT_TYPES } from '@/typings/card';
import { isEnglishSubject } from '@/utils';
import { cropImagesWithCanvas } from '@/utils/index';
export default {
  name: "essay-set",
  props: {
    aiEssayQuesData: {
      type: Array,
      default: () => []
    },
    isReadonly: {
      type: Boolean,
      default: false
    },
    subjectId: {
      type: String,
      default: ''
    },
    correctType: {
      type: Number,
      default: ICORRECT_TYPES.WEB
    },
    imgList: {
      type: Array,
      default: () => []
    },
    // 是否显示图片区域
    showImages: {
      type: Boolean,
      default: true
    },
    // 学科ID，用于判断是否为英语学科
    subjectId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      ICORRECT_TYPES,
      form: {
        ques: null,
        quesId: "",
        title: "",
        way: "",
        score: "",
        question: "",
        corrRule: "",
        type: "",
        isArbitration: true,
        beyondScore: 2,
        isCheckScore: false,
        wordCount: 0,
        firstLine: "",
        secondLine: "",
        demo: ""
      },
      rules: {
        quesId: [{ required: true, message: '请选择题目', trigger: 'change' }],
        way: [{ required: true, message: '请选择作文体裁', trigger: 'change' }],
        question: [{ required: true, message: '请输入作文题目', trigger: 'blur' }],
        corrRule: [{ required: true, message: '请输入批改要求', trigger: 'blur' }],
        type: [{ required: true, message: '请选择得分说明', trigger: 'change' }]
      },
      areaImgList: [],
      // 图片交互状态管理
      imageStates: {},
      // 缩放配置
      minScale: 1,
      maxScale: 3,
      scaleStep: 0.2,
      // 拖拽状态
      dragState: {
        isDragging: false,
        startX: 0,
        startY: 0,
        currentIndex: -1
      }
    };
  },
  watch: {
    "form.way": function (val) {
      if(this.isEnglishSubject && val == 2){
        this.rules.firstLine = [{ required: true, message: '请输入第一段首句', trigger: 'blur' }];
      }else{
        delete this.rules.firstLine;
      }
    }
  },
  computed: {
    // quesDataWithTips() {
    //   return this.aiEssayQuesData.map(item => {
    //     debugger
    //     if(this.isEnglishSubject) {
    //       return { ...item, hasTip: !item.question || !item.corrRule || !item.way }
    //     } else {
    //       return { ...item, hasTip: !item.question || !item.corrRule }
    //     }
    //   })
    // }
    isEnglishSubject() {
      return isEnglishSubject(this.subjectId);
    },

  },
  created() {
    if (this.aiEssayQuesData.length == 0) return;
    this.form.ques = this.aiEssayQuesData[0];
    this.changeQuesNo(this.form.ques);
  },
  methods: {
    async getExamAiCorrectQue() {
      let params = {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        workId: this.$route.query.workId,
        quesId: this.form.quesId,
        userId: this.$sessionSave.get("loginInfo").id,
        userName: this.$sessionSave.get("loginInfo").user_name,
      }
      let res = await getExamAiCorrectQueAPI(params);
      if (res.code == 1) {
        this.form.question = res.data[0].question;
        this.form.corrRule = res.data[0].corrRule;
        this.form.way = res.data[0].way;
        this.form.type = res.data[0].type || "";
        this.form.isArbitration = res.data[0].isArbitration == "" ? true : (res.data[0].isArbitration ?? true);
        this.form.beyondScore = res.data[0].beyondScore || 2;
        this.form.isCheckScore = res.data[0].isCheckScore || false;
        this.form.wordCount = res.data[0].wordCount || 0;  
        this.form.firstLine = res.data[0].firstLine;
        this.form.secondLine = res.data[0].secondLine;
        this.form.demo = res.data[0].demo;
      }
    },
    async saveExamAiCorrectQue() {
      let params = {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        workId: this.$route.query.workId,
        userId: this.$sessionSave.get("loginInfo").id,
        userName: this.$sessionSave.get("loginInfo").user_name,
        json: JSON.stringify({
          title: this.form.title,
          quesId: this.form.quesId,
          way: this.form.way,
          question: this.form.question,
          corrRule: this.form.corrRule,
          type: this.form.type,
          score: this.form.score,
          isArbitration: this.form.isArbitration,
          beyondScore: this.form.beyondScore,
          isCheckScore: this.form.isCheckScore,
          finalScore: 1,//双评分数计算 0:默认不处理 1:四舍五入保留一位小数(取0.5) 2:四舍五入取整
          wordCount: this.form.wordCount,
          firstLine: this.form.firstLine,
          secondLine: this.form.secondLine,
          demo: this.form.demo
        })
      }
      await saveExamAiCorrectQueAPI(params);
    },
    async changeQuesNo(ques) {
      this.form.ques = ques;
      this.form.score = ques.score;
      this.form.quesId = ques.quesNo;
      this.form.title = ques.quesNos;
      this.form.way = ques.way;
      await this.getExamAiCorrectQue();
      if (this.isEnglishSubject) {
        this.getEssayAreaImg(this.form.ques.points);
      }
    },
    async getEssayAreaImg(points) {
      this.areaImgList = [];
      this.areaImgList = await cropImagesWithCanvas(this.imgList, points);
      // 初始化图片状态
      this.initImageStates();
    },

    /**
     * 初始化图片状态
     */
    initImageStates() {
      this.imageStates = {};
      this.areaImgList.forEach((_, index) => {
        this.$set(this.imageStates, index, {
          scale: 1,
          translateX: 0,
          translateY: 0,
          naturalWidth: 0,
          naturalHeight: 0,
          containerWidth: 0,
          containerHeight: 0
        });
      });
    },

    /**
     * 获取图片容器样式
     */
    getViewportStyle(index) {
      return {
        cursor: this.dragState.isDragging && this.dragState.currentIndex === index ? 'grabbing' : 'grab',
        overflow: 'hidden',
        position: 'relative'
      };
    },

    /**
     * 获取图片样式
     */
    getImageStyle(index) {
      const state = this.imageStates[index];
      if (!state) return {};

      return {
        transform: `translate(${state.translateX}px, ${state.translateY}px) scale(${state.scale})`,
        transformOrigin: 'center center',
        transition: this.dragState.isDragging ? 'none' : 'transform 0.3s ease',
        width: '100%',
      };
    },

    /**
     * 鼠标滚轮缩放处理
     */
    handleWheel(event, index) {
      const state = this.imageStates[index];
      if (!state) return;

      const delta = event.deltaY > 0 ? -this.scaleStep : this.scaleStep;
      const newScale = Math.max(this.minScale, Math.min(this.maxScale, state.scale + delta));

      if (newScale !== state.scale) {
        // 计算缩放中心点
        const rect = event.currentTarget.getBoundingClientRect();
        const centerX = event.clientX - rect.left;
        const centerY = event.clientY - rect.top;

        // 计算缩放后的位移调整
        const scaleRatio = newScale / state.scale;
        const newTranslateX = centerX - (centerX - state.translateX) * scaleRatio;
        const newTranslateY = centerY - (centerY - state.translateY) * scaleRatio;

        this.$set(this.imageStates, index, {
          ...state,
          scale: newScale,
          translateX: newTranslateX,
          translateY: newTranslateY
        });

        // 限制拖拽范围
        this.$nextTick(() => {
          this.constrainImagePosition(index);
        });
      }
    },

    /**
     * 鼠标按下处理
     */
    handleMouseDown(event, index) {
      if (event.button !== 0) return; // 只处理左键

      const state = this.imageStates[index];
      if (!state) return; // 只有放大时才能拖拽

      this.dragState = {
        isDragging: true,
        startX: event.clientX - state.translateX,
        startY: event.clientY - state.translateY,
        currentIndex: index
      };

      event.preventDefault();
    },

    /**
     * 鼠标移动处理
     */
    handleMouseMove(event, index) {
      if (!this.dragState.isDragging || this.dragState.currentIndex !== index) return;

      const state = this.imageStates[index];
      if (!state) return;

      const newTranslateX = event.clientX - this.dragState.startX;
      const newTranslateY = event.clientY - this.dragState.startY;

      this.$set(this.imageStates, index, {
        ...state,
        translateX: newTranslateX,
        translateY: newTranslateY
      });
    },

    /**
     * 鼠标抬起处理
     */
    handleMouseUp(event, index) {
      if (this.dragState.isDragging && this.dragState.currentIndex === index) {
        this.dragState.isDragging = false;
        this.dragState.currentIndex = -1;

        // 限制拖拽范围
        this.constrainImagePosition(index);
      }
    },

    /**
     * 鼠标离开处理
     */
    handleMouseLeave(event, index) {
      this.handleMouseUp(event, index);
    },

    /**
     * 限制图片位置在合理范围内
     */
    constrainImagePosition(index) {
      const state = this.imageStates[index];
      if (!state) return;

      const container = document.querySelector(`.image-viewport:nth-child(${index + 1})`);
      if (!container) return;

      if(state.scale != this.minScale) return;

      const containerRect = container.getBoundingClientRect();
      const containerWidth = containerRect.width;
      const containerHeight = containerRect.height;

      // 计算图片实际显示尺寸
      const scaledWidth = state.naturalWidth * state.scale;
      const scaledHeight = state.naturalHeight * state.scale;

      // 计算允许的移动范围
      const maxTranslateX = Math.max(0, (scaledWidth - containerWidth) / 2);
      const maxTranslateY = Math.max(0, (scaledHeight - containerHeight) / 2);

      // 限制位移
      const constrainedTranslateX = Math.max(-maxTranslateX, Math.min(maxTranslateX, state.translateX));
      const constrainedTranslateY = Math.max(-maxTranslateY, Math.min(maxTranslateY, state.translateY));

      if (constrainedTranslateX !== state.translateX || constrainedTranslateY !== state.translateY) {
        this.$set(this.imageStates, index, {
          ...state,
          translateX: constrainedTranslateX,
          translateY: constrainedTranslateY
        });
      }
    },
    async submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          await this.$confirm(`开启作文题智批阅卷后，将无法再修改作文题分值，请确认作文题分值设置正确。当前设置的本题分值：${this.form.score}分`, '提示', {
            confirmButtonText: '已确认无误',
            cancelButtonText: '再确认下',
            type: 'warning'
          });
          this.saveExamAiCorrectQue();
          this.$emit('saveAICorrectQue');
        } else {
          this.$message.error('请完成必填项');
          return false;
        }
      });
    },

    /**
     * 图片加载错误处理
     */
    handleImageError(event, index) {
      console.error('图片加载失败:', this.form.images[index]);
      // 设置错误占位符
      event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwIiB5PSI1MCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjEyIiBmaWxsPSIjOTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+5Yqg6L295aSx6LSlPC90ZXh0Pjwvc3ZnPg==';
    }
  }
};
</script>

<style lang="scss" scoped>
.essay-setting-container {
  width: 100%;
  padding: 20px;
  background-color: #fff;

  .el-form-item {
    margin-bottom: 15px;

    ::v-deep .el-form-item__label {
      font-weight: 600;
    }

    &.subtitle {
      ::v-deep .el-form-item__label {
        font-weight: 100;
      }
    }

    .btn-item {
      margin-right: 8px;
      margin-bottom: 8px;
    }

    .ellipsis-btn {
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .form-block {
    display: inline-block;
  }

  .score-display {
    font-size: 16px;
    color: #67c23a;
    font-weight: bold;
  }

  .hint-text {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
  }

  .el-textarea {
    ::v-deep .el-textarea__inner {
      border-color: #dcdfe6;
      transition: all 0.3s;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }
    }
  }

  .essay-type-radio {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .radio-item {
    display: flex;
    flex-direction: column;
    padding: 12px 16px;
    transition: all 0.3s;

    &:hover {
      border-color: #C0C4CC;
      background-color: #F5F7FA;
    }

    .radio-desc {
      margin-top: 8px;
      margin-left: 24px;
      font-size: 13px;
      color: #606266;
      line-height: 1.5;
    }
  }

  /* 当单选框被选中时，高亮显示对应项 */
  .radio-item:has(.el-radio.is-checked) {
    border-color: #409EFF;
    background-color: #ecf5ff;
  }

  .el-button[type="primary"] {
    padding: 12px 25px;
    font-size: 14px;
  }

  // 表单内容包装器
  .form-content-wrapper {
    display: flex;
    gap: 20px;
    align-items: flex-start;

    // 响应式布局
    @media (max-width: 1200px) {
      flex-direction: column;
      gap: 15px;
    }
  }

  // 左侧表单区域
  .form-section {
    flex: 1;
    min-width: 0; // 防止flex子项溢出

    &.with-image {
      width: 65%;

      @media (max-width: 1200px) {
        width: 100%;
      }
    }
  }

  // 右侧图片显示区域
  .image-display-area {
    width: 30%;
    min-width: 280px;
    position: sticky;
    top: 20px;

    @media (max-width: 1200px) {
      width: 100%;
      position: static;
      min-width: auto;
    }
  }

  // 图片容器
  .image-container {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 16px;
    background-color: #fafafa;

    .image-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .image-title {
        font-size: 14px;
        font-weight: 500;
        color: #303133;
      }

      .add-image-btn {
        padding: 4px 8px;
        font-size: 12px;

        &:hover {
          color: #409eff;
        }
      }
    }
  }

  // 图片列表
  .image-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  // 图片项
  .image-item {
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    overflow: hidden;
    background-color: #fff;
    transition: all 0.3s;

    &:hover {
      border-color: #c0c4cc;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  // 图片包装器
  .image-wrapper {
    position: relative;
    width: 100%;
    overflow: hidden;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s;
    }
  }

  // 交互式图片容器样式
  .interactive-image-container {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    overflow: hidden;
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
  }

  // 图片视口
  .image-viewport {
    position: relative;
    width: 100%;
    overflow: hidden;
    background-color: #fafafa;
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;

    .interactive-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      cursor: grab;

      &:active {
        cursor: grabbing;
      }
    }

    // 缩放比例指示器
    .scale-indicator {
      position: absolute;
      top: 10px;
      right: 10px;
      background-color: rgba(0, 0, 0, 0.7);
      color: #fff;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      z-index: 5;
    }
  }
}
</style>