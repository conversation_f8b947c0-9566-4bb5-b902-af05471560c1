<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-05-22 14:28:02
 * @LastEditors: 小圆
-->
<template>
  <el-menu
    class="el-menu-vertical-demo"
    ref="ElMenu"
    :default-active="currentActiveMenuId"
    :unique-opened="true"
    @open="handleOpen"
    @select="selectMenu"
  >
    <!-- 常用报表 -->
    <report-menu-item
      v-if="oftenMenu.children.length && oftenMenuIndexList.length"
      :item="oftenMenu"
      @remove="removeFromOftenMenu"
    ></report-menu-item>
    <!-- 报表菜单 -->
    <report-menu-item
      v-for="item in currentMenuList"
      :item="item"
      :oftenMenuIndexList="oftenMenuIndexList"
      @add="addToOftenMenu"
    ></report-menu-item>
  </el-menu>
</template>

<script lang="ts">
import ReportMenuItem from '@/components/ExamReport/report-menu-item.vue';
import { getToken } from '@/service/auth.js';
import { listOftenReport, saveOftenReport } from '@/service/pexam';
import { ElMenu } from '@iclass/element-ui/types/menu';
import { Component, Inject, Vue, Watch } from 'vue-property-decorator';
import { getAllMenuList, getDefaultOftenMenuIndexList, MenuItemImpl, oftenName } from './constant';

@Component({
  components: {
    ReportMenuItem,
  },
})
export default class ReportMenu extends Vue {
  // 全部菜单列表
  allMenuList: MenuItemImpl[] = [];
  // 菜单列表映射
  menuMap: Map<string, MenuItemImpl> = new Map();
  // 当前菜单
  currentActiveMenuId: string = '';
  // 当前菜单列表
  currentMenuList: MenuItemImpl[] = [];
  // 常用菜单
  oftenMenu: MenuItemImpl = {
    title: '常用报表',
    index: oftenName,
    children: [],
  };
  // 常用菜单前缀
  oftenName = oftenName;
  // 常用菜单index列表
  oftenMenuIndexList: string[] = getDefaultOftenMenuIndexList();

  get ElMenu(): ElMenu {
    return this.$refs.ElMenu as ElMenu;
  }

  @Watch('$route.path')
  onRouteChange(path: string) {
    this.matchingRoutes();
  }

  created() {}

  // 初始化
  async init() {
    await this.initMenu();
    await this.getOftenReportList();
    this.setMenuList();
    this.matchingRoutes();
    if (!this.currentActiveMenuId) {
      this.selectFirstMenu();
    }
  }

  // 初始化菜单
  async initMenu() {
    let allMenuList = await getAllMenuList();
    this.allMenuList = [
      ...JSON.parse(JSON.stringify(allMenuList)),
      ...JSON.parse(JSON.stringify(allMenuList)).map(item => {
        return { ...item, index: oftenName + item.index };
      }),
    ];
    // 菜单列表映射
    this.menuMap = new Map(this.allMenuList.map(item => [item.index, item]));
  }

  // 匹配路由
  matchingRoutes(data?) {
    const localPath = data || this.$route.path;
    const reportType = this.$route.query.reportType || '';
    this.setMenuList();
    for (const item of this.allMenuList) {
      if (item.path == localPath) {
        this.currentActiveMenuId = reportType + item.index;
        break;
      }
    }
  }

  // 选择第一个菜单项
  selectFirstMenu() {
    let path = '';
    let reportType = '';

    // 路由参数存在menuId，则使用menuId，如果menu不在列表中，则选择第一个
    let menuId = this.$route.query.menuId as string;
    if (menuId && this.menuMap.get(menuId)) {
      path = this.menuMap.get(menuId).path;
      reportType = this.oftenMenuIndexList.find(item => item == menuId) ? oftenName : '';
    } else if (this.oftenMenu.children.length) {
      this.currentActiveMenuId = this.oftenMenu.children[0].index;
      path = this.oftenMenu.children[0].path;
      reportType = oftenName;
    } else {
      this.currentActiveMenuId = this.currentMenuList[0].children[0].index;
      path = this.currentMenuList[0].children[0].path;
    }
    this.$router.replace({
      path,
      query: {
        ...this.$route.query,
        reportType,
      },
    });
  }

  // 获取常用报表
  async getOftenReportList() {
    try {
      const res = await listOftenReport({ id: this.$sessionSave.get('loginInfo').id });
      const data = res.data || [];
      const defaultOftenMenuIndexList = getDefaultOftenMenuIndexList();
      this.oftenMenuIndexList = [...new Set([...defaultOftenMenuIndexList, ...data])];
    } catch (error) {
      const defaultOftenMenuIndexList = getDefaultOftenMenuIndexList();
      this.oftenMenuIndexList = defaultOftenMenuIndexList;
      this.saveOftenReport(defaultOftenMenuIndexList);
    }
  }

  // 保存常用报表
  async saveOftenReport(list: string[]) {
    return saveOftenReport({ token: getToken(), data: list });
  }

  // 设置菜单列表
  setMenuList() {
    const examOverviewMenu = {
      title: '考情分析',
      index: 'examOverview',
      children: [
        this.menuMap.get('overview'),
        this.menuMap.get('gradeDistribute'),
        this.menuMap.get('topAndDiff'),
        this.menuMap.get('online'),
        this.menuMap.get('limitStu'),
      ].filter(item => item),
    };
    examOverviewMenu.disabled = !examOverviewMenu.children.length;

    const classCompareMenu = {
      title: '成绩分析',
      index: 'classCompare',
      children: [
        this.menuMap.get('scoreSection'),
        this.menuMap.get('totalRank'),
        this.menuMap.get('rankSection'),
        this.menuMap.get('boxplot'),
      ].filter(item => item),
    };
    classCompareMenu.disabled = !classCompareMenu.children.length;

    const paperAnalyzeMenu = {
      title: '试卷分析',
      index: 'paperAnalyze',
      children: [
        this.menuMap.get('qualityReport'),
        this.menuMap.get('bothWayReport'),
        this.menuMap.get('quesTypeAvg'),
        this.menuMap.get('answerDetail'),
        this.menuMap.get('knowledgeAnalyze'),
      ].filter(item => item),
    };
    paperAnalyzeMenu.disabled = !paperAnalyzeMenu.children.length;

    const paperCommentMenu = this.menuMap.get('paperComment');
  
    let compositionAnalysisMenu = {
      title: '作文分析',
      index: 'compositionAnalysis',
      children: [
        this.menuMap.get('compositionClassSituation'),
        // this.menuMap.get('lexicalAnalysis'),
        this.menuMap.get('grammarAnalysis'),
        this.menuMap.get('referenceSample'),
      ].filter(item => item),
    }
    compositionAnalysisMenu = compositionAnalysisMenu.children.length ? compositionAnalysisMenu : null;

    this.currentMenuList = [examOverviewMenu, classCompareMenu, paperAnalyzeMenu, compositionAnalysisMenu, paperCommentMenu].filter(
      item => item
    );
    this.setOftenMenuList();
  }

  // 设置常用报表
  setOftenMenuList() {
    let menuList = [];
    this.oftenMenuIndexList.forEach(item => {
      if (this.menuMap.get(oftenName + item)) menuList.push(this.menuMap.get(oftenName + item));
    });
    this.oftenMenu.children = menuList;
  }

  // 添加到常用报表
  addToOftenMenu(item: MenuItemImpl['index']) {
    let index = item;
    this.oftenMenuIndexList.push(index);
    this.saveOftenReport(this.oftenMenuIndexList);
    this.setOftenMenuList();
    this.$message.success('添加成功');
  }

  // 删除常用报表
  removeFromOftenMenu(item: MenuItemImpl['index']) {
    let index = item.split(oftenName)[1];
    this.oftenMenuIndexList.splice(
      this.oftenMenuIndexList.findIndex(item => item == index),
      1
    );
    this.saveOftenReport(this.oftenMenuIndexList);
    this.setOftenMenuList();
    this.$message.success('移除成功');
  }

  // 打开菜单
  handleOpen(key, keyPath) {
    let menu;
    let index;
    if (keyPath.includes(oftenName)) {
      menu = this.oftenMenu;
    } else {
      menu = this.currentMenuList.find(item => item.index == key);
    }
    if (menu.children && menu.children.length) {
      index = menu.children[0].index;
    } else {
      index = menu.index;
    }
    let indexPath = [...keyPath, index];
    this.selectMenu(index, indexPath);
  }

  // 选择菜单
  async selectMenu(index, indexPath) {
    this.currentActiveMenuId = index;
    const path = this.allMenuList.find(item => item.index == index).path;
    this.$router.push({
      path: path,
      query: {
        ...this.$route.query,
        reportType: indexPath.includes(oftenName) ? oftenName : '',
      },
    });
    await this.$nextTick();
    document.body.scrollTop = document.documentElement.scrollTop = 0;
  }
}
</script>
