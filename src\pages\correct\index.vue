<!--
 * @Descripttion: 
 * @Author: 小圆
 * @Date: 2023-11-08 09:54:32
 * @LastEditors: 小圆
-->
<template>
  <div class="correct-wrapper">
    <el-container class="correct-container">
      <!--头部-->
      <el-header class="correct-header">
        <div class="title-back" :class="{ 'title-back--hidden': isFullscreen }">
          <div @click="$router.go(-1)" class="back-btn click-element">
            <i class="el-icon-arrow-left"></i><span>返回</span>
          </div>

          <span class="correct-state correct">
            <span class="text">复核模式</span>
            <span class="bg"></span>

            <span class="tip-text">（智批题由AI批改，老师可以进行复核，未复核不影响成绩发布）</span>
          </span>
        </div>

        <div class="select-header">
          <span class="header-item" v-if="source == SOURCE_TYPE.CLASS">
            <span> 班级 </span>
            <el-select class="select-item" v-model="classId" placeholder="请选择班级" @change="switchClass">
              <el-option v-for="item in classList" :key="item.id" :label="item.class_name" :value="item.id">
              </el-option>
            </el-select>
          </span>

          <span class="header-item">
            <span> 题目 </span>
            <el-select
              class="select-item"
              v-model="curQues"
              value-key="quesId"
              placeholder="请选择题目"
              @change="switchQues"
            >
              <el-option v-for="item in quesList" :key="item.quesId" :label="item.quesName" :value="item">
                <span class="ques-title ellipsis" :title="getReplyMessage(item)">{{ getReplyMessage(item) }}</span>
              </el-option>
            </el-select>
          </span>

          <span class="header-item">
            <span> 状态 </span>
            <el-select
              class="select-item"
              v-model="smartType"
              placeholder="请选择作答状态"
              filterable
              @change="onChangeSmartType"
            >
              <el-option v-for="item in smartTypeList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </span>

          <template v-if="quesType == 'compositionEva'">
            <span class="header-item">
              <span> 得分 </span>
              <el-select
                class="select-item"
                v-model="scoreOption"
                value-key="score"
                placeholder="请选择得分"
                filterable
                @change="onChangeStuOption"
              >
                <el-option
                  v-for="item in scoreList"
                  :label="item.score === '' ? '全部分数' : item.type == 'problem' ? `问题卷` : `${item.score}`"
                  :key="item.score"
                  :value="item"
                >
                  <span>{{
                    item.score === ''
                      ? '全部分数'
                      : item.type == 'problem'
                      ? `问题卷 (${item.count}份)`
                      : `${item.score} (${item.count}份)`
                  }}</span>
                </el-option>
              </el-select>
            </span>
          </template>

          <template v-if="quesType == 'fillEva'">
            <span class="header-item">
              <span> 学生作答 </span>
              <el-select
                class="select-item"
                v-model="type"
                placeholder="请选择学生作答"
                filterable
                @change="onChangeType"
              >
                <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </span>

            <span v-if="similarEnabled">
              <span class="header-item">
                相似度
                <el-popover
                  placement="top-start"
                  width="200"
                  trigger="hover"
                  content="相似度越高，越接近正确答案，可筛选相似度以减轻复核工作量"
                >
                  <i class="el-icon-question" slot="reference" style="vertical-align: top"></i>
                </el-popover>
              </span>

              <el-select class="correct-reply__select" style="width: 80px" v-model="similar" @change="onChangeSimilar">
                <el-option v-for="item in 10" :key="item" :value="(item - 1) * 10">
                  <span>{{ (item - 1) * 10 }}</span>
                </el-option>
              </el-select>
              <span>%~100%</span>

              <el-button style="margin-left: 10px" type="primary" @click="batchCorrect">批量处理</el-button>
            </span>
          </template>

          <span class="pull-right">
            <!-- 学生作答设置按钮 -->
            <el-popover placement="bottom" width="300" trigger="click" v-model="showAnswerSettings">
              <div class="answer-settings">
                <div class="setting-item">
                  <span>每行展示：</span>
                  <el-select v-model="answerLayout.col" placeholder="请选择行数" style="width: 80px">
                    <el-option :label="1" :value="1"></el-option>
                    <el-option :label="2" :value="2"></el-option>
                    <el-option :label="3" :value="3"></el-option>
                  </el-select>
                </div>
                <div class="setting-item" v-if="!isQuesCard">
                  <span>作答区域缩放：</span>
                  <el-input-number
                    v-model="answerLayout.scale"
                    controls-position="right"
                    :min="1"
                    :max="2"
                    :step="0.5"
                    :step-strictly="true"
                    style="width: 110px"
                  ></el-input-number>
                </div>
              </div>
              <el-button class="header-item" style="margin-left: 10px" slot="reference" icon="el-icon-setting"
                >设置</el-button
              >
            </el-popover>

            <!-- 全屏按钮 -->
            <el-button
              class="header-item"
              style="margin-left: 10px"
              :icon="isFullscreen ? 'el-icon-copy-document' : 'el-icon-full-screen'"
              @click="toggleFullscreen"
            >
              {{ isFullscreen ? '取消全屏' : '全屏' }}
            </el-button>

            <el-button
              v-if="smartType == REVIEW_STATE.WAIT_REVIEW && quesType == 'compositionEva' && stuList.length"
              type="primary"
              @click="batchCorrect"
              >批量通过AI评分</el-button
            >

            <template v-if="source == SOURCE_TYPE.CLASS">
              <el-button type="primary" @click="rePublish" :loading="publishLoading">重新发布</el-button>
              <span class="republish-tip">*全部修改完成请点击重新发布</span>
            </template>
          </span>
        </div>
      </el-header>

      <template v-if="curQues">
        <FillEva
          v-if="quesType == 'fillEva'"
          ref="fillEva"
          :curQues="curQues"
          :type="type"
          :smartType="smartType"
          :similar="similar"
          :subjectCenterCode="subjectCenterCode"
          :layout="answerLayout"
          :source="source"
          :classId="classId"
          @submit-next="onSubmitNext"
          @submit-current="onSubmitCurrent"
        ></FillEva>

        <compositionEva
          v-if="quesType == 'compositionEva'"
          ref="compositionEva"
          :scoreOption="scoreOption"
          :curQues="curQues"
          :type="type"
          :smartType="smartType"
          :subjectCenterCode="subjectCenterCode"
          :source="source"
          :classId="classId"
          @submit-next="onSubmitNext"
        ></compositionEva>
      </template>
    </el-container>
  </div>
</template>

<script lang="ts">
import { Component, Mixins, Ref, Vue } from 'vue-property-decorator';

import NoData from '@/components/noData.vue';
import ImgPreview from '@/components/SwiperViewer/ImgPreview.vue';
import { batchCorrectSmartQues, getExamQueScoreListAPI, getTeaCorrectQuesList, publishScoreAPI } from '@/service/api';
import { getSubjectCenterCode } from '@/utils/UserRoleUtils';
import CompositionEva from './compositionEva.vue';
import FillEva from './fillEva.vue';
import { PREVIEW_TYPE, SMART_TYPE, ScoreOption, SOURCE_TYPE, TeaCorrectQues } from './types';
import { IQUES_SCAN_MODE } from '@/typings/card';
import CorrectMixin from './correct.mixin.vue';

@Component({
  components: {
    NoData,
    ImgPreview,
    FillEva,
    CompositionEva,
  },
})
export default class Correct extends Mixins(CorrectMixin) {
  /** 填空题智批 */
  @Ref() fillEva: FillEva;
  /** 作文题智批 */
  @Ref() compositionEva: CompositionEva;

  SOURCE_TYPE = SOURCE_TYPE;

  // 类型
  quesType: 'fillEva' | 'compositionEva' = 'fillEva';
  // 当前作答模式
  type = PREVIEW_TYPE.ONLY_ERROR;
  // 作答模式选择列表
  typeOptions = [
    {
      label: '作答错误',
      value: PREVIEW_TYPE.ONLY_ERROR,
    },
    {
      label: '作答正确',
      value: PREVIEW_TYPE.ONLY_TRUE,
    },
  ];
  // 批改状态
  smartType = SMART_TYPE.WAIT_REVIEW;
  // 状态列表
  smartTypeList = [
    {
      label: '待复核',
      value: SMART_TYPE.WAIT_REVIEW,
    },
    {
      label: '已复核',
      value: SMART_TYPE.DONE_REVIEW,
    },
  ];
  // 题目列表
  quesList: TeaCorrectQues[] = [];
  // 当前题目
  curQues: TeaCorrectQues | null = null;
  // 相似度
  similar = 0;
  // 得分列表
  scoreList: ScoreOption[] = [];
  // 得分筛选
  scoreOption: ScoreOption | null = null;
  // 学科值
  subjectCenterCode = 'ENGLISH';

  REVIEW_STATE = SMART_TYPE;
  PREVIEW_TYPE = PREVIEW_TYPE;

  stuList = [];
  // 发布成绩loading
  publishLoading: boolean = false;
  // 是否全屏
  isFullscreen: boolean = false;
  // 是否显示作答设置弹窗
  showAnswerSettings: boolean = false;
  // 作答布局设置
  answerLayout = {
    col: 2,
    scale: 1,
    gap: 10,
  };
  // 0：教师任务批改 1：按班级批改
  source = SOURCE_TYPE.TEACHER;
  // 班级列表
  classList = [];
  // 当前班级ID
  classId = '';

  get isTestEnv() {
    return (
      process.env.NODE_ENV == 'development' ||
      process.env.VUE_APP_BASE_API == 'https://test.iclass30.com' ||
      this.$route.query.test == '1'
    );
  }

  get similarEnabled() {
    return (
      this.smartType == this.REVIEW_STATE.WAIT_REVIEW &&
      this.type == PREVIEW_TYPE.ONLY_ERROR &&
      this.quesType == 'fillEva' &&
      this.subjectCenterCode == 'ENGLISH'
    );
  }

  // 是否是题卡合一
  get isQuesCard() {
    return this.curQues?.cardType == 1; // 1:题卡合一
  }

  // 监听全屏状态变化
  mounted() {
    this.subjectCenterCode = getSubjectCenterCode(this.$route.query.subjectId as string) || 'ENGLISH';
    this.source = Number(this.$route.query.source) || SOURCE_TYPE.TEACHER;
    if (this.source == SOURCE_TYPE.CLASS) {
      this.classList = this.$sessionSave.get('innerClassList');
      this.classId = this.$route.query.classId as string;
    }

    this.getQuesList(true);

    // 监听全屏状态变化
    document.addEventListener('fullscreenchange', this.handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange);
    document.addEventListener('msfullscreenchange', this.handleFullscreenChange);
  }

  beforeDestroy() {
    // 移除全屏监听
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('msfullscreenchange', this.handleFullscreenChange);
  }

  // 获取题目列表
  async getQuesList(init = false) {
    const params: any = {
      workId: this.$route.query.workId,
      userId: this.$sessionSave.get('loginInfo').id,
      schoolId: this.$sessionSave.get('schoolInfo').id,
      smartCorrect: 1, // 智批改0：主观题阅卷 1：智批改阅卷
      correctVersion: 4,
    };
    if (this.source == SOURCE_TYPE.CLASS) {
      params.classId = this.classId;
      params.source = this.source;
    }
    const { code, data, msg } = await getTeaCorrectQuesList(params);
    if (this.checkPause(code, msg)) return;
    if (code !== 1 || (data && data.length == 0)) {
      return;
    }
    this.quesList = data;

    if (init) {
      let quesItem;
      for (let item of this.quesList) {
        if (item.errorNotReviewed > 0) {
          quesItem = item;
          break;
        }
      }
      this.switchQues(quesItem || this.quesList[0]);
    }
  }

  // 获取考试题目得分列表
  async getExamQueScoreList() {
    const params: any = {
      workId: this.$route.query.workId,
      userId: this.$sessionSave.get('loginInfo').id,
      schoolId: this.$sessionSave.get('schoolInfo').id,
      quesId: this.curQues.quesId,
      smartType: this.smartType,
    };
    if (this.source == SOURCE_TYPE.CLASS) {
      params.classId = this.classId;
      params.source = this.source;
    }

    const res = await getExamQueScoreListAPI(params).catch(error => {
      this.scoreList = [];
      return null;
    });
    if (!res) return;
    if (this.checkPause(res.code, res.msg)) return;
    if (res.code == 1) {
      let scoreList = res.data;
      scoreList.unshift({
        type: 'score',
        score: '',
      });
      scoreList.forEach(item => {
        if (item.type == 'problem') {
          item.score = 'problem';
        }
      });
      this.scoreList = scoreList;
    }
  }

  // 切换题目
  async switchQues(item) {
    this.curQues = item;
    this.quesType = this.getQuesType();
    await this.initExamQueScoreList();
    this.$nextTick(() => {
      this.initStuCorrect();
    });
  }

  // 切换班级
  async switchClass(id) {
    this.classId = id;
    this.getQuesList(true);
  }

  // 初始化批改
  async initStuCorrect() {
    if (this.quesType == 'fillEva') {
      await this.fillEva.getStuList({ page: 1 });
      this.stuList = this.fillEva.stuList;
    } else {
      await this.compositionEva.initCorrect();
      this.stuList = this.compositionEva.stuList;
    }
  }

  // 初始化考试题目得分列表
  async initExamQueScoreList() {
    if (this.quesType == 'compositionEva') {
      await this.getExamQueScoreList();
      this.scoreOption = this.scoreList[0];
    }
  }

  // 获取当前题目类型
  getQuesType(ques?: TeaCorrectQues) {
    if (!ques) ques = this.curQues;
    return ques.isAiCorrect == 3 ||
      ques.scanMode == IQUES_SCAN_MODE.AI_SUBJECT ||
      ques.scanMode == IQUES_SCAN_MODE.AI_ESSAY
      ? 'compositionEva'
      : 'fillEva';
  }

  // 切换学生作答
  onChangeType(val) {
    this.type = val;
    this.$nextTick(() => {
      this.initStuCorrect();
    });
  }

  // 切换批改状态
  async onChangeSmartType(item) {
    this.smartType = item;
    await this.initExamQueScoreList();
    this.$nextTick(async () => {
      this.initStuCorrect();
    });
  }

  // 提交下一题
  onSubmitNext() {
    this.getNextError();
  }

  // 提交当前题
  onSubmitCurrent() {
    this.getQuesList();
    this.$nextTick(() => {
      this.initStuCorrect();
    });
  }

  // 批量处理
  batchCorrect() {
    this.$confirm('批量处理后，所有待复核任务按识别结果处理，是否继续？', '批量处理', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      const params: any = {
        userId: this.$sessionSave.get('loginInfo').id,
        schoolId: this.$sessionSave.get('schoolInfo').id,
        workId: this.$route.query.workId as string,
        quesId: this.curQues.quesId,
        type: this.type,
        smartType: this.smartType,
        similar: this.quesType == 'compositionEva' ? 0 : this.similar,
        maxSimilar: 100,
        isRight: this.quesType == 'compositionEva' ? 0 : this.type == PREVIEW_TYPE.ONLY_ERROR ? 0 : 1,
        score: this.quesType == 'compositionEva' ? this.scoreOption.score : '',
        correctVersion: 4,
      };
      if (this.source == SOURCE_TYPE.CLASS) {
        params.classId = this.classId;
        params.source = this.source;
      }
      const res = await batchCorrectSmartQues(params);
      if (this.checkPause(res.code, res.msg)) return;
      if (res.code != 1) {
        return;
      }
      this.$message.success('批改成功');
      this.getNextError();
    });
  }

  // 获取下一题错误题目
  async getNextError() {
    // 更新题目列表
    await this.getQuesList();

    let index = this.quesList.findIndex(item => {
      return this.curQues.quesId == item.quesId;
    });
    if (index < 0) {
      return;
    }

    // 查找下一题待完成的题目
    let quesItem;
    for (let i = index + 1; i < this.quesList.length; i++) {
      const item = this.quesList[i];
      if (item.errorNotReviewed > 0) {
        quesItem = item;
        break;
      }
    }

    if (quesItem) {
      this.switchQues(quesItem);
    } else {
      this.$nextTick(() => {
        this.initStuCorrect();
      });
    }
  }

  // 更改相似度
  onChangeSimilar(val) {
    this.similar = val;
    this.$nextTick(() => {
      this.initStuCorrect();
    });
  }

  // 更改学生得分
  onChangeStuOption(val) {
    this.scoreOption = val;
    this.$nextTick(() => {
      this.initStuCorrect();
    });
  }

  /** 发布成绩 */
  async rePublish() {
    await this.$confirm('重新发布将重新统计学情，确定重新发布吗?<br />请确认全部学生修改完成且保存', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true,
    });
    this.publishLoading = true;

    try {
      const res = await publishScoreAPI({
        schoolId: this.$sessionSave.get('schoolInfo').id,
        workId: this.$route.query.workId as string,
      });
      if (res.code == 1) {
        this.$message.success('发布成绩成功');
      } else {
        this.$message.error(res.msg || '发布成绩失败');
      }
    } catch (error) {
      console.error(error);
    } finally {
      this.publishLoading = false;
    }
  }

  // 获取题目消息
  getReplyMessage(item: TeaCorrectQues) {
    if (this.smartType == SMART_TYPE.WAIT_REVIEW) {
      if (this.getQuesType(item) == 'compositionEva') {
        return `${item.quesName} - 待复核 (${item.errorNotReviewed}人)`;
      }

      if (this.type == PREVIEW_TYPE.ONLY_ERROR) {
        return `${item.quesName} - 待复核 (${item.errorNotReviewed}人)`;
      } else {
        return `${item.quesName} - 待复核 (${item.rightNotReviewed}人)`;
      }
    } else {
      if (this.getQuesType(item) == 'compositionEva') {
        return `${item.quesName} - 已复核 (${item.errorReviewed}人)`;
      }

      if (this.type == PREVIEW_TYPE.ONLY_ERROR) {
        return `${item.quesName} - 已复核 (${item.errorReviewed}人)`;
      } else {
        return `${item.quesName} - 已复核 (${item.rightReviewed}人)`;
      }
    }
  }

  // 切换全屏
  toggleFullscreen() {
    if (!this.isFullscreen) {
      this.enterFullscreen();
    } else {
      this.exitFullscreen();
    }
  }

  // 进入全屏
  enterFullscreen() {
    const element = document.documentElement;
    if (element.requestFullscreen) {
      element.requestFullscreen();
    } else if ((element as any).webkitRequestFullscreen) {
      (element as any).webkitRequestFullscreen();
    } else if ((element as any).msRequestFullscreen) {
      (element as any).msRequestFullscreen();
    }
    this.isFullscreen = true;
  }

  // 退出全屏
  exitFullscreen() {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if ((document as any).webkitExitFullscreen) {
      (document as any).webkitExitFullscreen();
    } else if ((document as any).msExitFullscreen) {
      (document as any).msExitFullscreen();
    }
    this.isFullscreen = false;
  }

  // 处理全屏状态变化
  handleFullscreenChange() {
    this.isFullscreen = !!(
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).msFullscreenElement
    );
  }
}
</script>

<style lang="scss" scoped>
// 主色
$mainColor: #3e73f6;
// 主要字号
$fontSize: 18px;
// 大标题字号
$bFontSize: 20px;
// 正文字号
$sFontSize: 16px;
// 头部,底部背景色
$borderBg: #f6f8fc;
// 提示信息颜色
$tipTextColor: rgba(165, 172, 189, 1);

.correct-wrapper {
  width: 100%;
  min-height: 100%;
  overflow-x: auto !important;
  overflow-y: auto !important;
  background-color: #f7fafc;
}

.correct-container {
  width: 1306px !important;
  height: 100%;
  margin: 0 auto;
  overflow: auto;
}

.correct-header {
  position: relative;
  padding: 20px;
  padding-bottom: 10px;
  height: auto !important;
  background: #fff;

  .header-item {
  }

  .select-item {
    ::v-deep .el-input__inner {
      width: 140px;
    }
  }
}

.title-back {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #fff;

  .back-btn {
    font-size: 16px;
    font-weight: 700;
    color: #3f4a54;
    margin-right: 10px;
  }

  .el-select {
    // margin: 0 10px;
  }

  ::v-deep .el-page-header__content {
    display: flex;
    align-items: center;
  }

  .blue-text {
    color: #0087e9;
    font-size: 14px;
  }

  .correct-state {
    display: inline-block;
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;

    width: 90px;
    height: 28px;
    font-size: 20px;
    font-weight: bold;
    color: #35424b;
    text-align: center;
    pointer-events: none;

    .text {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      z-index: 2;
    }

    .bg {
      display: inline-block;
      position: absolute;
      left: 25px;
      bottom: 2px;
      width: 41px;
      height: 7px;
      background: linear-gradient(90deg, #fa9926 0%, #ffffff 100%);
      border-radius: 4px;
      z-index: 1;
    }

    &.check {
      color: rgba(2, 202, 176, 1);
      border: 1px solid rgba(2, 202, 176, 1);
      background: linear-gradient(0deg, rgba(2, 202, 176, 0.1) 0%, rgba(2, 202, 176, 0.1) 100%);
    }

    .tip-text {
      position: absolute;
      color: rgba(165, 172, 189, 1);
      font-size: 12px;
      font-weight: 400;
      width: max-content;
      left: 90px;
      bottom: 0;
    }
  }

  .correct-log {
    position: absolute;
    right: 0;
    cursor: pointer;

    .correct-log-icon {
      width: 15px;
      height: 16px;
      vertical-align: middle;
    }
  }

  .correct-goon {
    position: absolute;
    right: 175px;
    top: 5px;
    width: 120px;
    height: 40px;
  }
}

.select-header {
  margin-top: 20px;
}

.republish-tip {
  position: absolute;
  right: 20px;
  top: 30px;
  font-size: 14px;
  color: #f56c6c;
}

// 全屏相关样式
.title-back--hidden {
  display: none !important;
}

.fullscreen-btn {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);

  .el-button {
    color: #409eff;
    font-size: 14px;

    &:hover {
      color: #66b3ff;
    }
  }
}

// 作答设置弹窗样式
.answer-settings {
  .setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    span {
      font-size: 14px;
      color: #606266;
      white-space: nowrap;
      margin-right: 12px;
    }
  }
}
</style>
